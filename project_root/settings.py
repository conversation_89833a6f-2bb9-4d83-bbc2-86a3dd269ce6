# --- Standard Library Imports ---
import os
import sys
from pathlib import Path
import dj_database_url
from decouple import config

# --- Local App Imports ---
from config.logging import LOGGING


# --- Base Directory ---
BASE_DIR = Path(__file__).resolve().parent.parent



# --- Logs Configuration ---
LOG_LEVEL = config('LOG_LEVEL', default='INFO').upper()



# --- Core Configuration ---
SECRET_KEY = config("SECRET_KEY")
DEBUG = config("DEBUG", default=False, cast=bool)
PLATFORM_FEE_RATE = config("PLATFORM_FEE_RATE", default=0.05, cast=float)
DASHBOARD_CACHE_TIMEOUT = config("DASHBOARD_CACHE_TIMEOUT", default=300, cast=int)
NOTIFICATION_CACHE_TIMEOUT = config("NOTIFICATION_CACHE_TIMEOUT", default=60, cast=int)
ENABLE_TEST_VIEW = DEBUG



# --- Allowed Hosts & CSRF ---
ALLOWED_HOSTS = ['.cozywish.com', 'cozywish.onrender.com']
RENDER_EXTERNAL_HOSTNAME = os.environ.get('RENDER_EXTERNAL_HOSTNAME')
if RENDER_EXTERNAL_HOSTNAME:
    ALLOWED_HOSTS.append(RENDER_EXTERNAL_HOSTNAME)
if DEBUG:
    ALLOWED_HOSTS.extend(['localhost', '127.0.0.1', 'testserver'])

CSRF_TRUSTED_ORIGINS = [
    'https://www.cozywish.com',
    'https://cozywish.onrender.com'
]




# --- Installed Applications ---
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third-party apps
    'storages',
    'widget_tweaks',
    'crispy_forms',
    'crispy_bootstrap4',

    # Project apps
    'accounts_app',
    'utility_app',
    'venues_app',
    'discount_app',
    'booking_cart_app',
    'payments_app',
    'dashboard_app',
    'review_app',
    'notifications_app',
    'admin_app',
    'utils',
]



# --- Middleware ---
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'admin_app.middleware.AdminCSPMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'dashboard_app.middleware.DashboardAccessMiddleware',
]



# --- URL Configuration ---
ROOT_URLCONF = 'project_root.urls'
WSGI_APPLICATION = 'project_root.wsgi.application'



# --- Templates ---
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'admin_app.context_processors.recent_admin_links',
                'booking_cart_app.context_processors.cart_context',
                'notifications_app.context_processors.notifications_context',
            ],
        },
    },
]



# --- Database Configuration ---
DATABASE_URL = os.environ.get('DATABASE_URL')
if DATABASE_URL:
    DATABASES = {'default': dj_database_url.parse(DATABASE_URL, conn_max_age=600)}
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }


# --- Test Database Configuration ---
if 'test' in sys.argv:
    DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }


# --- Password Validation ---
AUTH_PASSWORD_VALIDATORS = [
    {'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'},
    {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator'},
    {'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator'},
    {'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator'},
]


# --- Internationalization ---
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True


# --- Testing Flag ---
TESTING = 'test' in sys.argv


# --- Static Files Configuration ---
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']

if not DEBUG:
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
    WHITENOISE_USE_FINDERS = True
    WHITENOISE_AUTOREFRESH = True


# --- Media Files Configuration ---
if DEBUG:
    MEDIA_URL = '/media/'
    MEDIA_ROOT = BASE_DIR / 'media'
else:
    # AWS S3 Configuration
    AWS_ACCESS_KEY_ID = config('AWS_ACCESS_KEY_ID', default=None)
    AWS_SECRET_ACCESS_KEY = config('AWS_SECRET_ACCESS_KEY', default=None)
    AWS_STORAGE_BUCKET_NAME = config('AWS_STORAGE_BUCKET_NAME', default=None)
    AWS_S3_REGION_NAME = config('AWS_S3_REGION_NAME', default='us-east-1')
    AWS_S3_CUSTOM_DOMAIN = config('AWS_S3_CUSTOM_DOMAIN', default=None)

    if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY and AWS_STORAGE_BUCKET_NAME:
        # Storage backend configuration
        DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'

        # S3 specific settings
        AWS_S3_FILE_OVERWRITE = False
        AWS_DEFAULT_ACL = None
        AWS_S3_VERIFY = True
        AWS_S3_USE_SSL = True
        AWS_S3_SIGNATURE_VERSION = 's3v4'
        AWS_S3_ADDRESSING_STYLE = 'virtual'

        # Performance and caching
        AWS_S3_OBJECT_PARAMETERS = {
            'CacheControl': 'max-age=86400',
        }

        # URL configuration
        if AWS_S3_CUSTOM_DOMAIN:
            MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/'
        else:
            MEDIA_URL = f'https://{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com/'

        # Ensure URLs don't have double slashes
        if not MEDIA_URL.endswith('/'):
            MEDIA_URL += '/'

    else:
        # Log missing credentials for debugging
        missing_creds = []
        if not AWS_ACCESS_KEY_ID:
            missing_creds.append('AWS_ACCESS_KEY_ID')
        if not AWS_SECRET_ACCESS_KEY:
            missing_creds.append('AWS_SECRET_ACCESS_KEY')
        if not AWS_STORAGE_BUCKET_NAME:
            missing_creds.append('AWS_STORAGE_BUCKET_NAME')

        error_msg = f"AWS S3 credentials are required in production. Missing: {', '.join(missing_creds)}"
        raise Exception(error_msg)


# --- Email Configuration (SendGrid) ---
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD', default='')
if DEBUG and not EMAIL_HOST_PASSWORD:
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
else:
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'

EMAIL_HOST = config('EMAIL_HOST', default='smtp.sendgrid.net')
EMAIL_PORT = config('EMAIL_PORT', default=587, cast=int)
EMAIL_USE_TLS = config('EMAIL_USE_TLS', default=True, cast=bool)
EMAIL_HOST_USER = config('EMAIL_HOST_USER', default='apikey')
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL', default='<EMAIL>')
SERVER_EMAIL = config('SERVER_EMAIL', default='<EMAIL>')
EMAIL_TIMEOUT = 30


# --- User Model and Primary Key ---
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
AUTH_USER_MODEL = 'accounts_app.CustomUser'


# --- Authentication URLs ---
LOGIN_URL = '/accounts/customer/login/'
LOGOUT_REDIRECT_URL = '/'



# --- Celery Configuration ---
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
if 'test' in sys.argv:
    CELERY_TASK_ALWAYS_EAGER = True
    CELERY_TASK_EAGER_PROPAGATES = True


# --- Crispy Forms Configuration ---
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap4"
CRISPY_TEMPLATE_PACK = "bootstrap4"


# --- Production Security Settings ---
if not DEBUG:
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    SECURE_HSTS_SECONDS = ********
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_BROWSER_XSS_FILTER = True
    X_FRAME_OPTIONS = 'DENY'


# --- Forms URL Field Configuration ---
FORMS_URLFIELD_ASSUME_HTTPS = True







"""
## Production Environment Variables
AWS_ACCESS_KEY_ID
AWS_S3_CUSTOM_DOMAIN
AWS_S3_REGION_NAME
AWS_SECRET_ACCESS_KEY
AWS_STORAGE_BUCKET_NAME
DATABASE_URL
DEBUG
EMAIL_HOST
EMAIL_HOST_PASSWORD
EMAIL_HOST_USER
EMAIL_PORT
EMAIL_USE_TLS
LOG_LEVEL
SECRET_KEY
WEB_CONCURRENCY
"""